import { DashRoutesNavigation } from "@/shared/constants/AppRoutesNavigation";
import { Navigation } from "@toolpad/core/AppProvider";
import { Database, Handshake, LayoutDashboard, Users } from "lucide-react";

export const DASH_NAVIGATION: Navigation = [
  {
    kind: "header",
    title: "Principale",
  },
  {
    segment: DashRoutesNavigation.DASHBOARD,
    title: "Tableau de bord",
    icon: <LayoutDashboard />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Gestion des personnel",
  },
  {
    segment: DashRoutesNavigation.EMPLOYER,
    title: "Employer",
    icon: <Users />,
  },
  {
    segment: DashRoutesNavigation.STOCK,
    title: "Stock",
    icon: <Database />,
  },
  {
    kind: "divider",
  },
  {
    kind: "header",
    title: "Services",
  },
  {
    segment: DashRoutesNavigation.EMPLOYER,
    title: "Employees",
    icon: <Handshake />,
  },
  {
    segment: DashRoutesNavigation.HELP,
    title: "Assistance",
    icon: <Handshake />,
  },
];
