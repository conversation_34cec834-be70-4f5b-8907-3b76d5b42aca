import { Employer } from "@/domain/models/Employer";
import { supabase } from "@/infrastructure/supabase/supabase";
import { EMPLOYER_TABLE_NAME } from "./constants";
import { IGetEmployersByUserIdRepository } from "@/domain/interfaces/repositories/employer/IGetEmployersByUserIdRepository";

export class GetEmployersByUserIdRepository
  implements IGetEmployersByUserIdRepository
{
  async execute(id_professionel: number): Promise<Employer[]> {
    const { data, error } = await supabase
      .from(EMPLOYER_TABLE_NAME)
      .select("*")
      .eq("id_professionel", id_professionel);
    if (error) throw error;
    return (data as Employer[]) || [];
  }
}
