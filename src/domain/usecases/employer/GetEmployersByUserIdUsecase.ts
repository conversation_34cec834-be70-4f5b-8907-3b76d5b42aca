import { IGetEmployersByUserIdRepository } from "@/domain/interfaces/repositories/employer/IGetEmployersByUserIdRepository";
import { Employer } from "@/domain/models/Employer";

export class GetEmployersByUserIdUsecase {
  constructor(
    private readonly getEmployersByUserIdRepository: IGetEmployersByUserIdRepository
  ) {}

  async execute(id_professionel: number): Promise<Employer[]> {
    return this.getEmployersByUserIdRepository.execute(id_professionel);
  }
}
